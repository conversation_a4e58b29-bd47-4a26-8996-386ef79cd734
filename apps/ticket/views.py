from rest_framework.generics import ListAPIView, CreateAPIView, RetrieveAPIView, GenericAPIView, UpdateAPIView
from rest_framework.permissions import IsAuthenticated
from django.db.models import OuterRef, Subquery, Max
from django.db.models import Q
from apps.ticket.models import Ticket, TicketMessage
from apps.ticket.serializers import TicketSerializer, TicketMessageSerializer, CloseTicketSerializer
from apps.ticket.filters import IsReadFilterBackend
from rest_framework.response import Response
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from apps.account.models import User
from rest_framework.views import APIView
from apps.ticket.docs import ticket_message_create_swagger, close_ticket_swagger, ticket_message_list_swagger


class TicketListView(ListAPIView):
    serializer_class = TicketSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [IsReadFilterBackend] 

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name='is_read',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description='فیلتر بر اساس وضعیت خواندهشدن تیکت. مقدارها: true (خواندهشده) / false (خواندهنشده)'
            ),
            openapi.Parameter(
                name='deposit_id',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
            )
        ],
        responses={
            200: TicketSerializer(many=True),
        }
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    def get_queryset(self):
        user = self.request.user
        deposit_id = self.request.query_params.get('deposit_id')
            
        # استفاده از متد کلاس برای دریافت تیکت‌های کاربر
        queryset = Ticket.get_user_tickets(user)
        if deposit_id:
            queryset = queryset.filter(deposit_id=deposit_id)

        # اضافه کردن تاریخ آخرین پیام به هر تیکت
        latest_message_subquery = TicketMessage.objects.filter(
            ticket=OuterRef('pk')
        ).order_by('-created_at').values('created_at')[:1]
        
        queryset = queryset.annotate(
            last_message_date=Subquery(latest_message_subquery)
        )

        return queryset.order_by('-last_message_date')
    

class TicketCreateView(CreateAPIView):
    serializer_class = TicketSerializer
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'ticket_type': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=['ticket', 'report'],
                    description='نوع تیکت (ticket برای تیکت عادی، report برای گزارش)',
                    default='ticket'
                ),
                'deposit': openapi.Schema(type=openapi.TYPE_INTEGER, description='آیدی صندوق (الزامی برای ticket)'),
                'subject_id': openapi.Schema(type=openapi.TYPE_INTEGER, description='آیدی موضوع گزارش (الزامی برای report)'),
                'subject': openapi.Schema(type=openapi.TYPE_STRING, description='موضوع تیکت/گزارش'),
                'description': openapi.Schema(type=openapi.TYPE_STRING, description='توضیحات تیکت/گزارش'),
                'user_id': openapi.Schema(type=openapi.TYPE_INTEGER, description='آیدی کاربر (اختیاری).')
            },
            required=['subject', 'description']
        ),
        responses={
            201: TicketSerializer(),
        }
    )
    def post(self, request, *args, **kwargs):
        print(f'-TicketCreateView--> {request.data}')
        return super().post(request, *args, **kwargs)
    
    def perform_create(self, serializer):
        # بررسی وجود آیدی یوزر در درخواست (از طریق سریالایزر)
        user_id = serializer.validated_data.get('user_id')

        if user_id:
            try:
                # تلاش برای یافتن یوزر با آیدی ارسال شده
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                # اگر یوزر پیدا نشد، از یوزر فعلی استفاده می‌کنیم
                user = self.request.user
        else:
            # اگر آیدی یوزر ارسال نشده، از یوزر فعلی استفاده می‌کنیم
            user = self.request.user

        # Get validated data
        ticket_type = serializer.validated_data.get('ticket_type', Ticket.TicketType.TICKET)
        deposit = serializer.validated_data.get('deposit')
        report_subject = serializer.validated_data.get('report_subject')
        subject = serializer.validated_data.get('subject')
        description = serializer.validated_data.get('description')

        # Create ticket using the model's create_with_user method
        ticket = Ticket.create_with_user(
            deposit=deposit,
            subject=subject,
            description=description,
            user=user,
            request_user=self.request.user,
            # create_message=False  # We'll create the message manually to include type
        )

        # Update additional fields not covered by create_with_user
        ticket.ticket_type = ticket_type
        ticket.report_subject = report_subject
        ticket.save()

        # Create initial message
        TicketMessage.objects.create(
            ticket=ticket,
            user=user,
            content=description,
            message_type=TicketMessage.MessageType.TEXT
        )
        
        
        # به‌روزرسانی نمونه سریالایزر با تیکت ایجاد شده
        serializer.instance = ticket

class TicketMessageListView(ListAPIView):
    serializer_class = TicketMessageSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        ticket_id = self.kwargs.get('ticket_id')
        user = self.request.user
        # فقط پیام‌های مربوط به تیکت مورد نظر نمایش داده شوند
        return TicketMessage.objects.filter(ticket_id=ticket_id).order_by('created_at')

    @ticket_message_list_swagger
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        user = request.user

        # علامت‌گذاری همه پیام‌های خوانده‌نشده به‌عنوان خوانده‌شده
        queryset.exclude(user=user).filter(is_read=False).update(is_read=True)

        # دریافت اطلاعات تیکت قبل از pagination
        ticket_id = self.kwargs.get('ticket_id')
        try:
            from apps.ticket.models import Ticket
            ticket = Ticket.objects.select_related('user', 'deposit').get(id=ticket_id)

            # تشخیص اعضای تیکت یکبار
            participants = self._get_ticket_participants(ticket)

            # اطلاعات کامل تیکت
            ticket_info = self._get_ticket_info(ticket)

        except Exception as exp:
            print(f'-error-ticket_message_list-> {exp}')
            # در صورت خطا، اطلاعات پیش‌فرض
            participants = {"sender": {}, "receiver": {}}
            ticket_info = {}

        # دریافت response عادی با pagination
        response = super().list(request, *args, **kwargs)

        # اضافه کردن فیلد sender_id به هر پیام در results
        if 'results' in response.data:
            for message_data in response.data['results']:
                message_data['sender_id'] = message_data['user']['id']

        # اضافه کردن ticket_participants و ticket به response
        response.data['ticket_participants'] = participants
        response.data['ticket'] = ticket_info

        return response

    def _get_ticket_participants(self, ticket):
        """دریافت اطلاعات ارسال کننده و دریافت کننده تیکت"""
        participants = {
            "sender": {},
            "receiver": {}
        }

        # سازنده تیکت (همیشه sender اولیه است)
        ticket_creator = ticket.user
        participants["sender"] = {
            "id": ticket_creator.id,
            "fullname": ticket_creator.fullname,
            "role": "Ticket Creator",
            "type": "user"
        }

        # تشخیص receiver بر اساس نوع تیکت
        if ticket.ticket_type == 'report':
            # برای تیکت‌های گزارش، receiver پشتیبانی فنی است
            participants["receiver"] = {
                "id": 0,  # ID فرضی برای پشتیبانی
                "fullname": "پشتیبانی فنی",
                "role": "Support",
                "type": "support"
            }
        elif ticket.deposit:
            # برای تیکت‌های مربوط به صندوق، مدیران صندوق receiver هستند
            from apps.deposit.models import DepositMembership

            # پیدا کردن اولین مدیر فعال
            manager = DepositMembership.objects.filter(
                deposit=ticket.deposit,
                role__in=[DepositMembership.Role.ADMIN, DepositMembership.Role.OWNER],
                is_active=True
            ).select_related('user').first()

            if manager:
                # برای مدیران، نام صندوق نمایش داده می‌شود
                participants["receiver"] = {
                    "id": manager.user.id,
                    "fullname": ticket.deposit.title,  # نام صندوق
                    "role": manager.role,
                    "type": "manager"
                }
            else:
                # اگر مدیری نباشد، خود سازنده تیکت receiver هم هست
                participants["receiver"] = participants["sender"].copy()
        else:
            # برای تیکت‌های عمومی بدون صندوق، receiver پشتیبانی است
            participants["receiver"] = {
                "id": 0,  # ID فرضی برای پشتیبانی
                "fullname": "پشتیبانی عمومی",
                "role": "Support",
                "type": "support"
            }

        return participants

    def _get_ticket_info(self, ticket):
        """دریافت اطلاعات کامل تیکت"""
        ticket_data = {
            "id": ticket.id,
            "subject": ticket.subject,
            "ticket_type": ticket.ticket_type,
            "is_closed": ticket.is_closed,
            "created_at": ticket.created_at.isoformat(),
            "closed_at": ticket.closed_at.isoformat() if ticket.closed_at else None,
            "user": {
                "id": ticket.user.id,
                "fullname": ticket.user.fullname
            }
        }

        # اطلاعات صندوق (اگر وجود داشته باشد)
        if ticket.deposit:
            ticket_data["deposit"] = {
                "id": ticket.deposit.id,
                "title": ticket.deposit.title,
                "deposit_type": ticket.deposit.deposit_type
            }
        else:
            ticket_data["deposit"] = None

        # اطلاعات گزارش (اگر تیکت از نوع گزارش باشد)
        if ticket.ticket_type == 'report':
            ticket_data["report_subject_info"] = self._get_report_subject_info(ticket)
        else:
            ticket_data["report_subject_info"] = None

        return ticket_data

    def _get_report_subject_info(self, ticket):
        """دریافت اطلاعات موضوع گزارش"""
        try:
            if ticket.report_subject:
                return {
                    "id": ticket.report_subject.id,
                    "title": ticket.report_subject.title,
                    "description": ticket.report_subject.description,
                    "type": "report_subject"
                }
            return None
        except Exception as exp:
            print(f'---error--_get_report_subject_info--> {exp}')
            return {
                "id": None,
                "title": "موضوع نامشخص",
                "type": "unknown"
            }


    def _get_user_role_in_ticket(self, user, ticket):
        """تشخیص نقش کاربر در تیکت"""
        if ticket.user == user:
            return "Ticket Creator"

        if ticket.deposit:
            from apps.deposit.models import DepositMembership
            membership = DepositMembership.objects.filter(
                user=user,
                deposit=ticket.deposit,
                is_active=True
            ).first()
            if membership:
                return membership.role

        return "Member"
    
    
class TicketMessageCreateView(CreateAPIView):
    serializer_class = TicketMessageSerializer
    permission_classes = [IsAuthenticated]

    @ticket_message_create_swagger
    def post(self, request, *args, **kwargs):
        print(f'-TicketMessageCreateView--> {request.data}')
        return super().post(request, *args, **kwargs)

    def perform_create(self, serializer):
        ticket_id = self.kwargs.get('ticket_id')
        # تنظیم خودکار کاربر ارسال‌کننده پیام
        serializer.save(user=self.request.user, ticket_id=ticket_id)


class TicketMessageUpdateView(UpdateAPIView):
    serializer_class = TicketMessageSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        ticket_id = self.kwargs.get('ticket_id')
        return TicketMessage.objects.filter(ticket_id=ticket_id)
    
    def get_object(self):
        """Get the message object and check permissions"""
        message_id = self.kwargs.get('pk')
        ticket_id = self.kwargs.get('ticket_id')
        
        try:
            message = TicketMessage.objects.get(pk=message_id, ticket_id=ticket_id)
            
            # Only allow the original sender to edit the message
            if message.user != self.request.user:
                from rest_framework.exceptions import PermissionDenied
                raise PermissionDenied("You can only edit your own messages.")
            
            # Only allow editing text messages
            if message.message_type != TicketMessage.MessageType.TEXT:
                from rest_framework.exceptions import ValidationError
                raise ValidationError("Only text messages can be edited.")
                
            return message
            
        except TicketMessage.DoesNotExist:
            from django.http import Http404
            raise Http404("Message not found.")
    
    def patch(self, request, *args, **kwargs):
        """Handle PATCH request for editing message content"""
        print(f'-TicketMessageUpdateView--> {request.data}')
        return super().patch(request, *args, **kwargs)


class CloseTicketAPIView(GenericAPIView):
    """
    API view to close a ticket
    """
    serializer_class = CloseTicketSerializer
    permission_classes = [IsAuthenticated]

    @close_ticket_swagger
    def post(self, request, *args, **kwargs):
        """Close a ticket by its ID"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            ticket = serializer.save()

            # Return the updated ticket data
            ticket_serializer = TicketSerializer(ticket, context={'request': request})
            return Response({
                'status': 'success',
                'message': 'Ticket closed successfully.',
                'ticket': ticket_serializer.data
            }, status=status.HTTP_200_OK)

        return Response({
            'status': 'error',
            'message': 'Failed to close ticket.',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
