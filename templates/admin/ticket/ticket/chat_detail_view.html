{% extends "admin/base_site.html" %}
{% load i18n static admin_urls %}

{% block extrahead %}
    {{ block.super }}
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <script src="{% url 'admin:jsi18n' %}"></script>
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazirmatn@v33.003/Vazirmatn-font-face.css" rel="stylesheet" type="text/css" />
    <script src="https://unpkg.com/heroicons@2.0.18/24/outline/index.js"></script>
    {% comment %} <script src="{% static 'js/ticket_chat.js' %}" defer></script> {% endcomment %}
    <style>
/* Font family override */
body {
    font-family: 'Vazirmatn', -apple-system, BlinkMacSystemFont, 'Se<PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif !important;
}

/* Telegram-style Header */
.telegram-header {
    background: linear-gradient(135deg, #0088cc, #1c9ce8);
    border: none;
    box-shadow: 0 2px 8px rgba(0, 136, 204, 0.2);
    backdrop-filter: blur(20px);
    position: relative;
    z-index: 50;
    transition: all 0.3s ease;
}

.telegram-header:hover {
    box-shadow: 0 4px 16px rgba(0, 136, 204, 0.3);
}

.dark .telegram-header {
    background: linear-gradient(135deg, #1f2937, #374151);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Modern toggle switch */
.modern-toggle {
    position: relative;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
}

.modern-toggle input {
    position: absolute;
    opacity: 0;
}

.modern-toggle .slider {
    position: relative;
    width: 48px;
    height: 24px;
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    transition: all 0.3s ease;
}

.modern-toggle .slider::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 18px;
    height: 18px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.modern-toggle input:checked + .slider {
    background: rgba(34, 197, 94, 0.8);
    border-color: rgba(34, 197, 94, 0.4);
}

.modern-toggle input:checked + .slider::after {
    transform: translateX(24px);
    background: #22c55e;
}

/* Back Button styles */
.back-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    margin-right: 6px;
}

.back-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(-1px) scale(1.02);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    border-color: rgba(255, 255, 255, 0.25);
}

.back-button:active {
    transform: translateX(-0.5px) scale(0.98);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.back-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    transition: left 0.4s ease;
}

.back-button:hover::before {
    left: 100%;
}

/* Avatar styles */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    font-size: 14px;
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;
    backdrop-filter: blur(8px);
    margin-left: 8px;
    margin-right: 12px;
}

.user-avatar:hover {
    transform: translateY(-1px) scale(1.03);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.18);
    border-color: rgba(255, 255, 255, 0.35);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 9px;
    transition: all 0.3s ease;
}

.user-avatar:hover img {
    transform: scale(1.02);
}

.avatar-manager {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.avatar-support {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

/* Status badges */
.status-badge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.status-open {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-closed {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Header actions */
.header-action {
    padding: 7px 10px;
    border-radius: 9px;
    background: rgba(255, 255, 255, 0.12);
    color: rgba(255, 255, 255, 0.95);
    font-size: 12px;
    font-weight: 500;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.header-action:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
    border-color: rgba(255, 255, 255, 0.25);
}

/* Chat container */
.chat-container {
    height: calc(100vh - 250px);
    display: flex;
    flex-direction: column;
    background: #f8fafc;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.05);
}

.dark .chat-container {
    background: #111827;
}

/* Messages area */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: linear-gradient(to bottom, 
        rgba(255, 255, 255, 0.5), 
        rgba(248, 250, 252, 0.8)
    );
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
}

.dark .chat-messages {
    background: linear-gradient(to bottom, 
        rgba(17, 24, 39, 0.5), 
        rgba(31, 41, 55, 0.8)
    );
}

/* Message bubbles */
.message-bubble {
    max-width: 70%;
    margin-bottom: 16px;
    animation: slideInUp 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.message-sent {
    margin-left: auto;
    margin-right: 0;
    display: flex;
    justify-content: flex-end;
    width: 100%;
}

.message-received {
    margin-right: auto;
    margin-left: 0;
    display: flex;
    justify-content: flex-start;
    width: 100%;
}

/* Force right alignment for sent messages */
.message-sent > .flex {
    max-width: 70%;
    margin-left: auto;
}

/* Force left alignment for received messages */  
.message-received > .flex {
    max-width: 70%;
    margin-right: auto;
}

.message-bubble-sent {
    background: linear-gradient(135deg, #0088cc, #1c9ce8);
    color: white;
    border-radius: 20px 20px 8px 20px;
    box-shadow: 0 2px 12px rgba(0, 136, 204, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.message-bubble-received {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    color: #374151;
    border-radius: 20px 20px 20px 8px;
    border: 1px solid rgba(229, 231, 235, 0.3);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.dark .message-bubble-received {
    background: linear-gradient(135deg, #374151, #4b5563);
    color: #f9fafb;
    border: 1px solid rgba(75, 85, 99, 0.3);
}

.message-content {
    padding: 14px 18px;
    word-wrap: break-word;
    line-height: 1.5;
}

.message-meta {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 4px;
    text-align: right;
    font-weight: 500;
}

/* Small avatars in messages */
.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    flex-shrink: 0;
    font-size: 12px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.avatar-user {
    background: linear-gradient(135deg, #10b981, #059669);
}

/* Image message styles */
.message-image {
    max-width: 300px;
    border-radius: 12px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-image:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Telegram-style Input area */
.message-input-area {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: none;
    border-top: 1px solid rgba(229, 231, 235, 0.3);
    backdrop-filter: blur(20px);
    box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.05);
}

.dark .message-input-area {
    background: linear-gradient(135deg, #374151, #4b5563);
    border-top: 1px solid rgba(75, 85, 99, 0.3);
}

/* Telegram-style input container */
.telegram-input-container {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background: #ffffff;
    border: 2px solid rgba(0, 136, 204, 0.1);
    border-radius: 25px;
    padding: 8px 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.telegram-input-container:focus-within {
    border-color: #0088cc;
    box-shadow: 0 6px 30px rgba(0, 136, 204, 0.15);
    transform: translateY(-1px);
}

.dark .telegram-input-container {
    background: rgba(55, 65, 81, 0.9);
    border-color: rgba(75, 85, 99, 0.3);
}

.dark .telegram-input-container:focus-within {
    border-color: #1c9ce8;
    box-shadow: 0 6px 30px rgba(28, 156, 232, 0.15);
}

.message-input {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(229, 231, 235, 0.3);
    border-radius: 24px;
    padding: 12px 16px;
    min-height: 48px;
    max-height: 120px;
    resize: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* New telegram-style message input */
.telegram-message-input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    resize: none;
    font-size: 14px;
    line-height: 1.5;
    padding: 12px 16px;
    min-height: 20px;
    max-height: 150px;
    font-family: inherit;
    color: #374151;
    placeholder-color: #9ca3af;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.telegram-message-input::-webkit-scrollbar {
    display: none;
}

.dark .telegram-message-input {
    color: #f9fafb;
}

.telegram-message-input::placeholder {
    color: #9ca3af;
    opacity: 0.8;
}

/* Telegram-style action buttons */
.telegram-action-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    cursor: pointer;
    flex-shrink: 0;
}

.attach-btn {
    background: rgba(156, 163, 175, 0.1);
    color: #6b7280;
}

.attach-btn:hover {
    background: rgba(156, 163, 175, 0.2);
    transform: scale(1.05);
}

.send-btn-telegram {
    background: linear-gradient(135deg, #0088cc, #1c9ce8);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 136, 204, 0.3);
}

.send-btn-telegram:hover {
    background: linear-gradient(135deg, #006ca3, #0088cc);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 136, 204, 0.4);
}

.send-btn-telegram:active {
    transform: scale(0.95);
}

.dark .attach-btn {
    background: rgba(75, 85, 99, 0.3);
    color: #9ca3af;
}

.dark .attach-btn:hover {
    background: rgba(75, 85, 99, 0.5);
}

.message-input:focus {
    outline: none;
    border-color: #0088cc;
    box-shadow: 0 0 0 3px rgba(0, 136, 204, 0.1), 0 4px 16px rgba(0, 136, 204, 0.1);
    transform: translateY(-1px);
}

.dark .message-input {
    background: rgba(55, 65, 81, 0.8);
    border-color: rgba(75, 85, 99, 0.3);
    color: #f9fafb;
}

/* Modern buttons */
.image-upload-btn {
    width: 48px;
    height: 48px;
    border-radius: 24px;
    background: rgba(0, 136, 204, 0.1);
    color: #0088cc;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(0, 136, 204, 0.2);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.image-upload-btn:hover {
    background: rgba(0, 136, 204, 0.2);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 136, 204, 0.2);
}

.send-btn {
    background: linear-gradient(135deg, #0088cc, #1c9ce8);
    color: white;
    border: none;
    border-radius: 24px;
    padding: 12px 24px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 136, 204, 0.3);
}

.send-btn:hover {
    background: linear-gradient(135deg, #006ca3, #0088cc);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 136, 204, 0.4);
}

.send-btn:active {
    transform: translateY(0);
}

/* Enhanced image preview */
.image-preview {
    background: rgba(248, 250, 252, 0.95);
    border: 2px solid rgba(0, 136, 204, 0.2);
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);
    animation: slideDown 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .image-preview {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(28, 156, 232, 0.3);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}



/* Custom scrollbar */
.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(0, 136, 204, 0.3);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 136, 204, 0.5);
}

/* Closed ticket notice */
.closed-ticket-notice {
    background: linear-gradient(135deg, rgba(107, 114, 128, 0.1), rgba(156, 163, 175, 0.05));
    border: 1px solid rgba(107, 114, 128, 0.2);
    border-radius: 16px;
    backdrop-filter: blur(10px);
}

.dark .closed-ticket-notice {
    background: linear-gradient(135deg, rgba(75, 85, 99, 0.2), rgba(107, 114, 128, 0.1));
    border-color: rgba(75, 85, 99, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
    /* Header adjustments */
    .telegram-header {
        padding: 12px 16px;
    }
    
    .telegram-header .flex {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .back-button {
        width: 32px;
        height: 32px;
        margin-right: 4px;
    }
    
    .user-avatar {
        width: 36px;
        height: 36px;
        font-size: 13px;
        margin-left: 4px;
        margin-right: 8px;
    }
    
    /* Hide some header text on very small screens */
    .telegram-header h2 {
        font-size: 16px;
        max-width: 150px;
    }
    
    .telegram-header p {
        font-size: 12px;
        max-width: 200px;
    }
    
    /* Header actions - stack on small screens */
    .telegram-header .flex:last-child {
        flex-direction: column;
        align-items: flex-end;
        gap: 4px;
    }
    
    .header-action {
        padding: 6px 8px;
        font-size: 11px;
        min-width: auto;
    }
    
    .status-badge {
        padding: 3px 8px;
        font-size: 10px;
    }

    /* Chat container */
    .chat-container {
        height: calc(100vh - 180px);
        margin: 4px;
        border-radius: 12px;
    }
    
    /* Messages area */
    .chat-messages {
        padding: 12px;
    }

    /* Message bubbles */
    .message-bubble {
        max-width: 90%;
        margin-bottom: 12px;
    }
    
    .message-content {
        padding: 12px 14px;
        font-size: 14px;
    }
    
    .message-meta {
        font-size: 10px;
    }

    .message-avatar {
        width: 28px;
        height: 28px;
        font-size: 10px;
    }
    
    /* Image messages */
    .message-image {
        max-width: 250px;
    }
    
    /* Input area */
    .message-input-area {
        padding: 12px 16px;
    }
    
    .telegram-input-container {
        gap: 8px;
        padding: 6px 10px;
        border-radius: 20px;
    }
    
    .telegram-message-input {
        padding: 10px 12px;
        font-size: 14px;
        min-height: 18px;
        max-height: 120px;
    }
    
    .telegram-action-btn {
        width: 36px;
        height: 36px;
    }
    
    /* Image preview */
    .image-preview {
        padding: 12px;
        margin-bottom: 12px;
    }
    
    .image-preview img {
        max-width: 200px;
        max-height: 120px;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .telegram-header {
        padding: 10px 12px;
    }
    
    .telegram-header .flex:first-child {
        min-width: 0;
        flex: 1;
    }
    
    .telegram-header .flex:last-child {
        flex-shrink: 0;
    }
    
    .user-avatar {
        width: 32px;
        height: 32px;
        font-size: 12px;
        margin-left: 2px;
        margin-right: 6px;
    }
    
    .telegram-header h2 {
        font-size: 15px;
        max-width: 120px;
    }
    
    .telegram-header p {
        font-size: 11px;
        max-width: 150px;
    }
    
    .header-action {
        padding: 5px 6px;
        font-size: 10px;
    }
    
    .status-badge {
        padding: 2px 6px;
        font-size: 9px;
    }
    
    .chat-container {
        height: calc(100vh - 160px);
        margin: 2px;
        border-radius: 8px;
    }
    
    .chat-messages {
        padding: 8px;
    }
    
    .message-bubble {
        max-width: 95%;
        margin-bottom: 10px;
    }
    
    .message-content {
        padding: 10px 12px;
        font-size: 13px;
    }
    
    .message-image {
        max-width: 200px;
    }
    
    .message-input-area {
        padding: 8px 12px;
    }
    
    .telegram-input-container {
        gap: 6px;
        padding: 4px 8px;
    }
    
    .telegram-message-input {
        padding: 8px 10px;
        font-size: 13px;
    }
    
    .telegram-action-btn {
        width: 32px;
        height: 32px;
    }
    
    .image-preview {
        padding: 8px;
        margin-bottom: 8px;
    }
    
    .image-preview img {
        max-width: 150px;
        max-height: 100px;
    }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .chat-container {
        height: calc(100vh - 120px);
    }
    
    .telegram-header {
        padding: 8px 16px;
    }
    
    .telegram-header h2 {
        font-size: 14px;
    }
    
    .telegram-header p {
        font-size: 11px;
    }
    
    .message-input-area {
        padding: 8px 16px;
    }
}

/* Touch-friendly improvements */
@media (max-width: 768px) {
    /* Larger touch targets */
    .back-button,
    .telegram-action-btn,
    .header-action {
        min-height: 44px;
        min-width: 44px;
    }
    
    /* Better spacing for touch */
    .message-bubble {
        margin-bottom: 16px;
    }
    
    /* Prevent zoom on input focus */
    .telegram-message-input {
        font-size: 16px;
    }
    
    /* Smooth scrolling */
    .chat-messages {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }
    
    /* Better button spacing */
    .telegram-header .flex:last-child {
        gap: 8px;
    }
    
    /* Improved message content readability */
    .message-content {
        line-height: 1.6;
    }
    
    /* Better image handling on mobile */
    .message-image {
        max-width: 100%;
        height: auto;
    }
    
    /* Prevent horizontal scroll */
    body {
        overflow-x: hidden;
    }
    
    /* Better container margins */
    .max-w-7xl {
        padding-left: 8px;
        padding-right: 8px;
    }
}

/* Loading animations */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 16px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(107, 114, 128, 0.6);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) { animation-delay: 0s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Message status */
.message-status {
    display: inline-flex;
    align-items: center;
    margin-left: 4px;
}

.status-sent { color: rgba(156, 163, 175, 0.8); }
.status-delivered { color: rgba(0, 136, 204, 0.6); }
.status-read { color: rgba(34, 197, 94, 0.8); }

/* Loading spinner for select */
.status-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.status-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* Select box styling */
select#ticket-status-select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left 8px center;
    background-size: 16px;
    padding-left: 32px;
}

select#ticket-status-select option {
    background: #374151;
    color: white;
    padding: 8px;
}

/* Custom notification styles */
.notification-success {
    background: linear-gradient(135deg, #10b981, #059669);
    border-left: 4px solid #34d399;
}

.notification-error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border-left: 4px solid #f87171;
}

.notification-info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border-left: 4px solid #60a5fa;
}

/* Kebab Menu Styles */
.message-kebab-container {
    position: relative;
    display: inline-block;
}

.message-kebab-btn {
    background: none;
    border: none;
    padding: 4px 6px;
    border-radius: 4px;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    color: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.message-bubble:hover .message-kebab-btn {
    opacity: 1;
}

.message-kebab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

.kebab-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.kebab-dot {
    width: 3px;
    height: 3px;
    background: currentColor;
    border-radius: 50%;
    margin: 1px 0;
}

.message-kebab-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 120px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.2s ease;
}

.message-kebab-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dark .message-kebab-menu {
    background: #374151;
    border-color: rgba(75, 85, 99, 0.3);
}

.kebab-menu-item {
    display: block;
    width: 100%;
    padding: 10px 16px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
    transition: background-color 0.15s ease;
    border-radius: 0;
    text-decoration: none;
}

.kebab-menu-item:first-child {
    border-radius: 8px 8px 0 0;
}

.kebab-menu-item:last-child {
    border-radius: 0 0 8px 8px;
}

.kebab-menu-item:only-child {
    border-radius: 8px;
}

.kebab-menu-item:hover {
    background: #f3f4f6;
    text-decoration: none;
    color: #374151;
}

.dark .kebab-menu-item {
    color: #f9fafb;
}

.dark .kebab-menu-item:hover {
    background: #4b5563;
    color: #f9fafb;
}

/* Edit mode styles */
.message-edit-container {
    position: relative;
}

.message-edit-input {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #0088cc;
    border-radius: 12px;
    padding: 12px 16px;
    width: 100%;
    resize: none;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    min-height: 60px;
    max-height: 200px;
    color: #374151;
    outline: none;
    box-shadow: 0 2px 8px rgba(0, 136, 204, 0.15);
}

.dark .message-edit-input {
    background: rgba(55, 65, 81, 0.95);
    color: #f9fafb;
    border-color: #1c9ce8;
}

.message-edit-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    justify-content: flex-end;
}

.edit-action-btn {
    padding: 6px 12px;
    border-radius: 6px;
    border: none;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 60px;
}

.edit-save-btn {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.edit-save-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
}

.edit-cancel-btn {
    background: rgba(156, 163, 175, 0.2);
    color: #6b7280;
}

.edit-cancel-btn:hover {
    background: rgba(156, 163, 175, 0.3);
}

/* Loading state */
.message-edit-loading .edit-save-btn {
    opacity: 0.6;
    cursor: not-allowed;
}

.message-edit-loading .edit-save-btn::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}
    </style>

    <script>
        // Global ticket state for tracking status changes
        let ticketState = {
            is_closed: {{ ticket_info.is_closed|yesno:"true,false" }}
        };

        document.addEventListener('DOMContentLoaded', function() {
            // Chat functionality and other interactive features
            // Status toggle is now handled by form submission
            
            // Auto-resize textarea for long messages
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                messageInput.addEventListener('input', function() {
                    // Reset height to recalculate
                    this.style.height = 'auto';
                    // Set height based on scroll height
                    this.style.height = Math.min(this.scrollHeight, 150) + 'px';
                });

                // Handle Enter key (send on Enter, newline on Shift+Enter)
                messageInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        if (e.shiftKey) {
                            // Allow newline
                            return;
                        } else {
                            // Send message
                            e.preventDefault();
                            const form = document.getElementById('message-form');
                            if (form && this.value.trim()) {
                                console.log('Submitting message:', this.value.trim()); // Debug log
                                form.submit();
                            }
                        }
                    }
                });
            }
            
            // Image upload functionality
            const imageBtn = document.getElementById('image-btn');
            const imageInput = document.getElementById('image-input');
            const imagePreview = document.getElementById('image-preview');
            const previewImg = document.getElementById('preview-img');
            const removeImageBtn = document.getElementById('remove-image');
            const messageTypeInput = document.getElementById('message-type');
            
            console.log('Image elements:', { imageBtn, imageInput, imagePreview, previewImg, removeImageBtn, messageTypeInput });
            
            if (imageBtn && imageInput) {
                imageBtn.addEventListener('click', function() {
                    console.log('Image button clicked');
                    imageInput.click();
                });
                
                imageInput.addEventListener('change', function(e) {
                    console.log('Image input changed', e.target.files);
                    const file = e.target.files[0];
                    if (file) {
                        console.log('File selected:', file.name, file.size);
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            if (previewImg) {
                                previewImg.src = e.target.result;
                            }
                            if (imagePreview) {
                                imagePreview.classList.remove('hidden');
                            }
                            if (messageTypeInput) {
                                messageTypeInput.value = 'image';
                                console.log('Message type set to:', messageTypeInput.value);
                            }
                        };
                        reader.readAsDataURL(file);
                    } else {
                        console.log('No file selected');
                    }
                });
                
                if (removeImageBtn) {
                    removeImageBtn.addEventListener('click', function() {
                        console.log('Remove image clicked');
                        imageInput.value = '';
                        if (imagePreview) {
                            imagePreview.classList.add('hidden');
                        }
                        if (messageTypeInput) {
                            messageTypeInput.value = 'text';
                        }
                        if (previewImg) {
                            previewImg.src = '';
                        }
                        console.log('Image removed, message type:', messageTypeInput ? messageTypeInput.value : 'null');
                    });
                }
            } else {
                console.log('Image button or input not found');
            }
            
            // Handle form submission validation
            const messageForm = document.getElementById('message-form');
            if (messageForm) {
                messageForm.addEventListener('submit', function(e) {
                    const messageInput = document.getElementById('message-input');
                    const imageInput = document.getElementById('image-input');
                    const messageTypeInput = document.getElementById('message-type');
                    
                    console.log('Form submit event triggered'); // Debug log
                    console.log('Message input value:', messageInput ? messageInput.value : 'null');
                    console.log('Image files:', imageInput ? imageInput.files.length : 'null');
                    console.log('Message type:', messageTypeInput ? messageTypeInput.value : 'null');
                    console.log('Image input element:', imageInput);
                    console.log('Image input files:', imageInput ? imageInput.files : 'null');
                    
                    // Check if we have either text content or an image
                    const hasText = messageInput && messageInput.value.trim().length > 0;
                    const hasImage = imageInput && imageInput.files && imageInput.files.length > 0;
                    const messageType = messageTypeInput ? messageTypeInput.value : 'text';
                    
                    console.log('Has text:', hasText);
                    console.log('Has image:', hasImage);
                    console.log('Message type from input:', messageType);
                    
                    // If message type is 'image', we should have an image file
                    // If message type is 'text', we should have text content
                    if (!hasText && !hasImage) {
                        e.preventDefault();
                        alert('{% trans "Please provide a message or image." %}');
                        if (messageInput) messageInput.focus();
                        return false;
                    }
                    
                    console.log('Form validation passed, submitting...');
                    
                    // Don't clear the form immediately - let it submit first
                    // The form will be cleared after successful submission by the page reload
                });
            }
            
            // Notification function
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-20 right-6 z-50 px-4 py-3 rounded-lg shadow-xl text-white transition-all duration-300 transform translate-x-full notification-${type}`;
                notification.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <div class="flex-shrink-0">
                            ${type === 'success' ? 
                                '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' :
                            type === 'error' ? 
                                '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>' :
                                '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
                            }
                        </div>
                        <p class="text-sm font-medium">${message}</p>
                    </div>
                `;
                
                document.body.appendChild(notification);
                
                // Slide in
                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                }, 100);
                
                // Auto hide after 4 seconds
                setTimeout(() => {
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 4000);
            }

            // Message editing functionality
            window.toggleKebabMenu = function(messageId) {
                const menu = document.getElementById(`kebab-menu-${messageId}`);
                const isShown = menu.classList.contains('show');
                
                // Close all other menus
                document.querySelectorAll('.message-kebab-menu.show').forEach(m => {
                    m.classList.remove('show');
                });
                
                // Toggle current menu
                if (!isShown) {
                    menu.classList.add('show');
                }
            };

            window.editMessage = function(messageId, currentContent) {
                // Close kebab menu
                const menu = document.getElementById(`kebab-menu-${messageId}`);
                menu.classList.remove('show');
                
                // Find the message content element
                const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
                if (!messageElement) {
                    console.error('Message element not found');
                    return;
                }
                
                const contentElement = messageElement.querySelector('.message-content p');
                if (!contentElement) {
                    console.error('Content element not found');
                    return;
                }
                
                // Create edit form
                const editContainer = document.createElement('div');
                editContainer.className = 'message-edit-container';
                editContainer.innerHTML = `
                    <textarea class="message-edit-input" placeholder="{% trans 'Enter your message...' %}">${currentContent}</textarea>
                    <div class="message-edit-actions">
                        <button type="button" class="edit-action-btn edit-cancel-btn" onclick="cancelEdit(${messageId})">
                            {% trans "Cancel" %}
                        </button>
                        <button type="button" class="edit-action-btn edit-save-btn" onclick="saveMessage(${messageId})">
                            {% trans "Save" %}
                        </button>
                    </div>
                `;
                
                // Store original content
                editContainer.dataset.originalContent = currentContent;
                
                // Replace content with edit form
                contentElement.parentNode.replaceChild(editContainer, contentElement);
                
                // Focus on textarea
                const textarea = editContainer.querySelector('textarea');
                textarea.focus();
                textarea.setSelectionRange(textarea.value.length, textarea.value.length);
                
                // Auto-resize textarea
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 200) + 'px';
                });
                
                // Trigger initial resize
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
            };

            window.cancelEdit = function(messageId) {
                const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
                const editContainer = messageElement.querySelector('.message-edit-container');
                const originalContent = editContainer.dataset.originalContent;
                
                // Restore original content
                const contentElement = document.createElement('p');
                contentElement.className = 'text-sm leading-relaxed';
                contentElement.textContent = originalContent;
                
                editContainer.parentNode.replaceChild(contentElement, editContainer);
            };

            window.saveMessage = function(messageId) {
                const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
                const editContainer = messageElement.querySelector('.message-edit-container');
                const textarea = editContainer.querySelector('textarea');
                const newContent = textarea.value.trim();
                
                if (!newContent) {
                    alert('{% trans "Message content cannot be empty." %}');
                    return;
                }
                
                // Show loading state
                editContainer.classList.add('message-edit-loading');
                const saveBtn = editContainer.querySelector('.edit-save-btn');
                saveBtn.disabled = true;
                
                // Prepare CSRF token
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                
                // Send AJAX request
                fetch(`{% url 'admin:ticket_ticket_edit_message' ticket.id 0 %}`.replace('0', messageId), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken,
                    },
                    body: JSON.stringify({
                        'content': newContent
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update content and restore normal view
                        const contentElement = document.createElement('p');
                        contentElement.className = 'text-sm leading-relaxed';
                        contentElement.textContent = data.new_content;
                        editContainer.parentNode.replaceChild(contentElement, editContainer);
                        
                        // Show success notification
                        showNotification(data.message, 'success');
                    } else {
                        // Show error
                        alert(data.error || '{% trans "Failed to update message." %}');
                        editContainer.classList.remove('message-edit-loading');
                        saveBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('{% trans "An error occurred while updating the message." %}');
                    editContainer.classList.remove('message-edit-loading');
                    saveBtn.disabled = false;
                });
            };

            // Close kebab menus when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.message-kebab-container')) {
                    document.querySelectorAll('.message-kebab-menu.show').forEach(menu => {
                        menu.classList.remove('show');
                    });
                }
            });
        });
    </script>
{% endblock %}

{% block breadcrumbs %}{% endblock %}

{% block content %}
    <!-- Telegram-Style Header -->
    <div class="telegram-header px-6 py-4">
        <div class="flex items-center justify-between">
            <!-- Left Side: Back Button + Avatar + User Info -->
            <div class="flex items-center min-w-0 flex-1">
                <!-- Back Button -->
                <a href="{% url 'admin:ticket_ticket_changelist' %}" 
                   class="back-button group flex-shrink-0"
                   title="{% trans 'Back to Ticket List' %}">
                    <svg class="w-5 h-5 text-white transform transition-transform group-hover:-translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                
                <!-- User Avatar -->
                <div class="user-avatar group flex-shrink-0">
                    <img src="{% static 'images/ticket1.png' %}" 
                         alt="{{ participants.receiver.fullname }}" 
                         title="{{ participants.receiver.fullname }}"
                         class="group-hover:brightness-110">
                </div>
                
                <!-- User Info -->
                <div class="flex flex-col min-w-0 flex-1">
                    <div class="flex items-center space-x-2 md:space-x-5">
                        <h2 class="text-lg font-semibold text-white truncate">
                            {{ participants.receiver.fullname }}
                        </h2>
                        <span class="status-badge {% if ticket_info.is_closed %}status-closed{% else %}status-open{% endif %} flex-shrink-0 hidden sm:inline-block">
                            {% if ticket_info.is_closed %}{% trans "Closed" %}{% else %}{% trans "Active" %}{% endif %}
                        </span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <p class="text-sm text-white text-opacity-75 truncate">
                            {{ ticket.subject }} 
                        </p>
                        <!-- Status badge for mobile - shown below title -->
                        <span class="status-badge {% if ticket_info.is_closed %}status-closed{% else %}status-open{% endif %} flex-shrink-0 sm:hidden">
                            {% if ticket_info.is_closed %}{% trans "Closed" %}{% else %}{% trans "Active" %}{% endif %}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Right Side: Actions -->
            <div class="flex items-center gap-2 flex-shrink-0">
                <!-- Message Count -->
                <div class="header-action hidden sm:flex">
                    <svg class="w-4 h-4 inline-block mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.003 9.003 0 01-5.52-1.87l-3.48.24 1.44-3.12C2.96 14.52 3 13.27 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <span class="font-medium">{{ messages_count }}</span>
                </div>
                
                <!-- Message Count for mobile - icon only -->
                <div class="header-action sm:hidden">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.003 9.003 0 01-5.52-1.87l-3.48.24 1.44-3.12C2.96 14.52 3 13.27 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <span class="font-medium text-xs">{{ messages_count }}</span>
                </div>
                
                <!-- Status Toggle with Form -->
                <form method="post" action="{% url 'admin:ticket_ticket_toggle_status' ticket.id %}" class="inline">
                    {% csrf_token %}
                    {% if ticket_info.is_closed %}
                        <input type="hidden" name="action" value="open">
                        <!-- Desktop button -->
                        <button type="submit" class="header-action bg-transparent border border-white border-opacity-25 text-white rounded-lg px-3 py-2 text-sm cursor-pointer hover:bg-white hover:bg-opacity-15 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-40 font-medium hidden sm:flex">
                            <svg class="w-4 h-4 inline-block mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
                            </svg>
                            {% trans "Reopen Ticket" %}
                        </button>
                        <!-- Mobile button - icon only -->
                        <button type="submit" class="header-action bg-transparent border border-white border-opacity-25 text-white rounded-lg p-2 cursor-pointer hover:bg-white hover:bg-opacity-15 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-40 sm:hidden" title="{% trans 'Reopen Ticket' %}">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    {% else %}
                        <input type="hidden" name="action" value="close">
                        <!-- Desktop button -->
                        <button type="submit" class="header-action bg-transparent border border-white border-opacity-25 text-white rounded-lg px-3 py-2 text-sm cursor-pointer hover:bg-white hover:bg-opacity-15 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-40 font-medium hidden sm:flex">
                            <svg class="w-4 h-4 inline-block mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            {% trans "Close Ticket" %}
                        </button>
                        <!-- Mobile button - icon only -->
                        <button type="submit" class="header-action bg-transparent border border-white border-opacity-25 text-white rounded-lg p-2 cursor-pointer hover:bg-white hover:bg-opacity-15 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-40 sm:hidden" title="{% trans 'Close Ticket' %}">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </button>
                    {% endif %}
                </form>

            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 py-6 md:px-6">
        <div class="chat-container">
            
            <!-- Chat Messages Area -->
            <div class="chat-messages">
                {% if chat_messages %}
                    {% for message in chat_messages %}
                        <!-- Determine if message should be on the right (manager or support) or left (user) -->
                        {% if message.sender_type == "manager" or message.sender_type == "support" %}
                            <!-- Manager/Support messages (right side) -->
                            <div class="message-bubble message-sent" data-message-id="{{ message.id }}">
                                <div class="flex items-start gap-3 justify-end">
                                    <!-- Message Content -->
                                    <div class="message-bubble-sent">
                                        <!-- User name for sent messages from managers/support -->
                                        <div class="px-4 pt-3 pb-1 text-right">
                                            <p class="text-xs font-semibold text-white text-opacity-90">
                                                {{ message.sender_info.name }}
                                                {% if message.sender_info.role and message.sender_info.role != "Member" %}
                                                    <span class="text-white text-opacity-70">• {{ message.sender_info.role }}</span>
                                                {% endif %}
                                            </p>
                                        </div>
                                        
                                        <div class="message-content">
                                            <!-- Image message -->
                                            {% if message.message_type == 'image' and message.content_image %}
                                                <img src="{{ message.content_image.url }}" 
                                                     alt="{% trans 'Image message' %}" 
                                                     class="message-image"
                                                     onclick="window.open('{{ message.content_image.url }}', '_blank')"
                                                     title="{% trans 'Click to enlarge' %}">
                                            {% endif %}
                                            
                                            <!-- Text content -->
                                            {% if message.content %}
                                                <p class="text-sm leading-relaxed">{{ message.content }}</p>
                                            {% endif %}
                                            
                                            <!-- Message metadata -->
                                            <div class="message-meta flex items-center justify-end space-x-2 mt-2">
                                                <span>{{ message.created_at|date:"H:i" }}</span>
                                                
                                                <!-- Kebab menu for text messages -->
                                                {% if message.message_type == 'text' %}
                                                <div class="message-kebab-container">
                                                    <button type="button" class="message-kebab-btn" onclick="toggleKebabMenu({{ message.id }})">
                                                        <div class="kebab-icon">
                                                            <div class="kebab-dot"></div>
                                                            <div class="kebab-dot"></div>
                                                            <div class="kebab-dot"></div>
                                                        </div>
                                                    </button>
                                                    <div class="message-kebab-menu" id="kebab-menu-{{ message.id }}">
                                                        <button type="button" class="kebab-menu-item" onclick="editMessage({{ message.id }}, '{{ message.content|escapejs }}')">
                                                            {% trans "Edit" %}
                                                        </button>
                                                    </div>
                                                </div>
                                                {% endif %}
                                                
                                                <div class="message-status">
                                                    {% if message.is_read %}
                                                        <svg class="w-4 h-4 status-read" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                        </svg>
                                                    {% else %}
                                                        <svg class="w-4 h-4 status-sent" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                        </svg>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- User Avatar (right side) -->
                                    <div class="user-avatar avatar-{{ message.sender_type }}">
                                        {% if message.user.avatar_url %}
                                            <img src="{{ message.user.avatar_url }}" 
                                                 alt="{{ message.sender_info.name }}" 
                                                 class="w-full h-full object-cover rounded-full">
                                        {% else %}
                                            {{ message.sender_info.name|slice:":2"|upper }}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <!-- User messages (left side) -->
                            <div class="message-bubble message-received" data-message-id="{{ message.id }}">
                                <div class="flex items-start gap-3">
                                    <!-- User Avatar (left side) -->
                                    <div class="user-avatar avatar-{{ message.sender_type }}">
                                        {% if message.user.avatar_url %}
                                            <img src="{{ message.user.avatar_url }}" 
                                                 alt="{{ message.sender_info.name }}" 
                                                 class="w-full h-full object-cover rounded-full">
                                        {% else %}
                                            {{ message.sender_info.name|slice:":2"|upper }}
                                        {% endif %}
                                    </div>
                                    
                                    <!-- Message Content -->
                                    <div class="message-bubble-received">
                                        <!-- User name for received messages -->
                                        <div class="px-4 pt-3 pb-1">
                                            <p class="text-xs font-semibold text-blue-600 dark:text-blue-400">
                                                {{ message.sender_info.name }}
                                                {% if message.sender_info.role and message.sender_info.role != "Member" %}
                                                    <span class="text-gray-500 dark:text-gray-300">• {{ message.sender_info.role }}</span>
                                                {% endif %}
                                            </p>
                                        </div>
                                        
                                        <div class="message-content">
                                            <!-- Image message -->
                                            {% if message.message_type == 'image' and message.content_image %}
                                                <img src="{{ message.content_image.url }}" 
                                                     alt="{% trans 'Image message' %}" 
                                                     class="message-image"
                                                     onclick="window.open('{{ message.content_image.url }}', '_blank')"
                                                     title="{% trans 'Click to enlarge' %}">
                                            {% endif %}
                                            
                                            <!-- Text content -->
                                            {% if message.content %}
                                                <p class="text-sm leading-relaxed">{{ message.content }}</p>
                                            {% endif %}
                                            
                                            <!-- Message metadata -->
                                            <div class="message-meta flex items-center justify-end space-x-2 mt-2">
                                                <span>{{ message.created_at|date:"H:i" }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                {% else %}
                    <!-- Empty State -->
                    <div class="flex flex-col items-center justify-center h-full py-16">
                        <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-gray-700 dark:to-gray-600 rounded-full flex items-center justify-center">
                            <svg class="w-10 h-10 text-blue-500 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.003 9.003 0 01-5.52-1.87l-3.48.24 1.44-3.12C2.96 14.52 3 13.27 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">{% trans "No messages yet" %}</h3>
                        <p class="text-gray-500 dark:text-gray-400 text-center max-w-sm">
                            {% trans "Send your first message in this conversation" %}
                        </p>
                    </div>
                {% endif %}
            </div>
            
            <!-- Message Input Area -->
            {% if not ticket_info.is_closed %}
                <div class="message-input-area">
                    <div class="max-w-4xl mx-auto px-6 py-4">
                    <form method="post" action="{% url 'admin:ticket_ticket_add_message' ticket.id %}" enctype="multipart/form-data" id="message-form">
                        {% csrf_token %}
                        
                        <!-- Image preview area (hidden by default) -->
                        <div id="image-preview" class="image-preview mb-4 hidden">
                            <div class="relative inline-block">
                                <img id="preview-img" src="" alt="{% trans 'Preview' %}" class="max-w-xs max-h-32 rounded-lg border">
                                <button 
                                    type="button" 
                                    id="remove-image"
                                    class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center font-bold shadow-lg transition-all duration-200"
                                    title="{% trans 'Remove image' %}"
                                >
                                    ×
                                </button>
                            </div>
                        </div>
                        
                        <div class="telegram-input-container">
                            <!-- Message input -->
                            <textarea 
                                name="content"
                                class="telegram-message-input"
                                rows="1"
                                placeholder="{% trans 'Type your message...' %}"
                                id="message-input"
                            ></textarea>
                            <!-- Hidden inputs -->
                            <input 
                                type="file" 
                                name="content_image" 
                                accept="image/*"
                                id="image-input"
                                class="hidden"
                            >
                            <input type="hidden" name="message_type" value="text" id="message-type">
                            
                            <!-- Action buttons -->
                            <!-- Image upload button -->
                            <button 
                                type="button"
                                id="image-btn"
                                class="telegram-action-btn attach-btn"
                                title="{% trans 'Attach image' %}"
                            >
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                </svg>
                            </button>
                            
                            <!-- Send button -->
                            <button 
                                type="submit"
                                class="telegram-action-btn send-btn-telegram"
                                id="send-btn"
                            >
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </form>
                    </div>
                </div>
            {% else %}
                <!-- Closed ticket notice -->
                <div class="closed-ticket-notice p-6">
                    <div class="text-center">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            {% trans "Ticket Closed" %}
                        </h3>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            {% trans "This ticket has been closed and no new messages can be sent." %}
                        </p>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}